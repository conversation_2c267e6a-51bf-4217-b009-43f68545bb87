# Flatmate GUI: Widget Creation Guide

**Date**: July 22, 2025  
**Status**: Active Guideline  

## 1. Philosophy: Simple, Composable, Stylable

Our approach to creating GUI components is guided by the lessons learned from the July 2025 refactoring attempt. The goal is to build a library of reusable widgets that are easy to use, maintain, and style.

*   **Simple**: Favour clear, direct code. Avoid unnecessary layers of abstraction.
*   **Composable**: Build complex widgets by combining simpler ones.
*   **Stylable**: All components must be easily styled via the central QSS stylesheet.

If it isn't simple, it's wrong. If it breaks styling, it's wrong.

## 2. The Widget Creation Workflow

Follow these steps when creating a new widget.

### Step 1: Check for Existing Components & Styles

Before writing any code, check if a similar component already exists in `fm/gui/_shared_components/widgets/`.

Next, **review the main stylesheet** to see if existing styles can be leveraged:

*   **Stylesheet Location**: `fm/gui/styles/theme.qss`

Look for existing selectors for base Qt types (`QPushButton`, `Q<PERSON>abel`, `QComboBox`) or custom properties (`[type="action_btn"]`) that you can reuse.

### Step 2: Choose the Correct Design Pattern

Select one of the two approved patterns based on the widget's complexity.

#### Pattern A: Composition (for Compound Widgets)

Use this when your widget combines **multiple** child widgets.

*   **Inherit from `QWidget`**.
*   Create child widgets (`QLabel`, `QPushButton`, etc.) in the `__init__`.
*   Arrange children using a layout (`QVBoxLayout`, `QHBoxLayout`).
*   Use `setObjectName()` or `setProperty()` on the **child widgets** to expose them to QSS.

**Reference Example**: `fm/gui/_shared_components/widgets/option_menus.py`

```python
# CORRECT: Composition Pattern
from PySide6.QtWidgets import QWidget, QLabel, QComboBox, QVBoxLayout

class OptionMenuWithLabel(QWidget):
    def __init__(self, label_text, options, parent=None):
        super().__init__(parent)
        
        # 1. Create child widgets
        self.label = QLabel(label_text)
        self.combo_box = QComboBox()
        self.combo_box.addItems(options)

        # 2. Set object names for styling
        self.label.setObjectName("subheading")

        # 3. Arrange in a layout
        layout = QVBoxLayout(self)
        layout.addWidget(self.label)
        layout.addWidget(self.combo_box)
```

#### Pattern B: Simple Inheritance (for Variants)

Use this when your widget is a simple **variant** of a single Qt widget.

*   **Inherit directly** from the specific Qt class (`QPushButton`, `QLabel`, etc.).
*   In the `__init__`, call `super()` and then use `setProperty()` or `setObjectName()` to apply the specific style hook.

**Reference Example**: `fm/gui/_shared_components/widgets/buttons.py`

```python
# CORRECT: Simple Inheritance Pattern
from PySide6.QtWidgets import QPushButton

class ActionButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        # Set property for QSS styling
        self.setProperty("type", "action_btn")
```

### Step 3: Add to Shared Components

Place your new widget in a logical file within `fm/gui/_shared_components/widgets/` (e.g., `labels.py`, `sliders.py`).

Update the `__init__.py` in that directory to expose your new widget for easy importing.

## 3. What to Avoid at All Costs

To prevent repeating past failures, **we must strictly avoid** the following:

*   **❌ Abstract Base Classes**: Do not use a generic `BaseWidget` that other components inherit from. It adds a useless layer of complexity.
*   **❌ Wrapper Widgets**: Do not create widgets that wrap other widgets in a container. This breaks QSS selectors and is the primary reason the previous refactor failed.
*   **❌ Configuration Objects**: Do not use `Config` objects to pass styling or text information. Set properties and text directly.
*   **❌ Factory Patterns**: These are over-engineering for our needs.

If you find yourself creating any of these, stop and return to one of the two approved patterns above.
