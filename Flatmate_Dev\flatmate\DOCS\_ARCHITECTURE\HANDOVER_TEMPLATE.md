# Work Session Handover

---

## Session Details

- **Session Type**: `[REFACTOR | FEATURE | MAINTENANCE]`
- **Primary Goal**: `[A clear, one-sentence description of the main objective.]`
- **Version**: `1.0`

## 1. Core Task: The Implementation Plan

This work session is to execute the pre-approved plan. All investigation is complete; this is a **code implementation phase**.

**The full, detailed plan is located here:**
- **Plan Document**: `[Link to the final plan in the PLANNING session's FINAL_PLAN directory]`

**You must follow the steps in the plan precisely.** Do not deviate without initiating a new planning/discussion session.

## 2. Context & Background

For full context on how this plan was developed, including all evidence and analysis, refer to the original planning session artifacts.

- **Planning Session Directory**: `[Link to the root of the PLANNING_<name> directory]`

## 3. Key Protocols & Guides

All work must adhere to the following project standards:

- **Unified Work Session**: `DOCS/_PROTOCOLS/WORKFLOWS/unified-work-session.md`
- **Widget Creation Guide**: `DOCS/_ARCHITECTURE/GUI/WIDGET_CREATION_GUIDE.md` (if applicable)
- `[Add links to any other relevant protocols]`

## 4. Success Criteria

The work is considered complete when:

1.  All steps in the implementation plan have been successfully executed.
2.  The application runs without errors.
3.  The validation steps outlined in the plan have been performed and have passed.
4.  All changes are committed with a clear message referencing this work session.
