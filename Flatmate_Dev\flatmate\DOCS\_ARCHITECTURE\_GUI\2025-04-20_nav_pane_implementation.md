# NavPane Implementation Guide

## Overview

The NavPane component provides a vertical navigation bar with module icons. It follows clean architectural principles with proper separation of concerns and well-defined interfaces between components.

## Components

### 1. NavButton

A custom QToolButton that displays an icon with consistent styling:

- Uses the centralized IconRenderer for SVG rendering with color control
- Supports visual states (normal, hover, selected)
- Handles icon coloring consistently across the application

### 2. NavPane

A container widget that manages a collection of NavButtons:

- Emits signals when buttons are clicked
- Provides methods to highlight buttons programmatically
- Maintains the visual state of the navigation

## Communication Flow

### User-Initiated Navigation

1. User clicks a navigation button
2. NavPane highlights the button
3. NavPane publishes a `navigationSelected` signal with the module ID
4. <PERSON><PERSON>le Coordinator (subscriber) receives the signal
5. <PERSON>dule Coordinator activates the requested module

```
User → NavButton → NavPane → Signal → Module Coordinator → Module
```

### Programmatic Navigation

1. Module is activated through other means (e.g., direct call, event)
2. Module Coordinator activates the module
3. Module Coordinator calls `nav_pane.highlight_item(module_id)`
4. NavPane updates the visual state without emitting signals

```
Other Trigger → Module Coordinator → Module + NavPane.highlight_item()
```

## Integration Example

### In MainWindow

```python
# In main_window.py
from fm.gui.components.nav_pane import NavPane
from fm.modules.module_coordinator import ModuleCoordinator

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # Create components
        self.nav_pane = NavPane()
        self.module_coordinator = ModuleCoordinator()
        
        # Connect navigation signal to module activation
        self.nav_pane.navigationSelected.connect(
            self.module_coordinator.activate_module
        )
        
        # Connect module activation signal back to nav pane
        self.module_coordinator.moduleActivated.connect(
            self.nav_pane.highlight_item
        )
```

### In Module Coordinator

```python
# In module_coordinator.py
from PySide6.QtCore import QObject, Signal

class ModuleCoordinator(QObject):
    # Signal emitted when a module is activated
    moduleActivated = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.modules = {}  # Dictionary of available modules
        self.current_module = None
    
    def register_module(self, module_id, module):
        """Register a module with the coordinator."""
        self.modules[module_id] = module
    
    def activate_module(self, module_id):
        """Activate a module by its ID."""
        # Deactivate current module if any
        if self.current_module:
            self.current_module.deactivate()
        
        # Activate requested module
        if module_id in self.modules:
            self.modules[module_id].activate()
            self.current_module = self.modules[module_id]
            
            # Publish module activation signal
            self.moduleActivated.emit(module_id)
```

## Best Practices

1. **Clean Interfaces** - Components communicate through signals rather than direct access
2. **Separation of Concerns** - NavPane handles only UI, Module Coordinator handles only module management
3. **Bidirectional Communication** - Both components can trigger updates in the other through well-defined interfaces
4. **Visual Consistency** - IconRenderer ensures consistent icon rendering across the application
5. **Fail Fast** - Invalid module IDs will raise errors immediately rather than being silently handled

## Future Enhancements

1. **Theme Support** - Extend IconRenderer to support dynamic theming
2. **Accessibility** - Add keyboard navigation support
3. **Dynamic Modules** - Support for dynamically adding/removing navigation items
4. **State Persistence** - Remember the last active module between sessions
