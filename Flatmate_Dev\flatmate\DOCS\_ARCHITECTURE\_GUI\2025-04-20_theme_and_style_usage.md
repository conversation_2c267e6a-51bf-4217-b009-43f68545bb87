# Theme and Style Usage in FlatMate

## Current Architecture

The FlatMate application uses a three-tier styling system:

1. **palette.qss** - The foundation layer that defines color variables using CSS custom properties
2. **theme.qss** - The middle layer that imports the palette and applies variables to widget types
3. **style.qss** - The legacy layer with additional styling (often using hardcoded values)

## What's Actually Being Used

### Primary Usage Pattern

The current implementation predominantly uses:

- **palette.qss** for color definitions (using CSS variables)
- **theme.qss** for applying those colors to general widget types
- Both files are loaded and combined by the `load_styles()` function

### Redundancy and Inconsistency

There are several issues with the current implementation:

1. **Duplicate Styling**: 
   - `style.qss` often duplicates rules from `theme.qss` but with hardcoded values
   - Example: `theme.qss` uses `var(--color-bg-dark)` while `style.qss` uses `#1E1E1E`

2. **Mixed Approaches**:
   - Some components use object names (`#left_panel`)
   - Some use property-based styling (`QPushButton[type="primary"]`)
   - Some use class-based styling (`QToolButton[class="nav_button"]`)

3. **Stylesheet Loading**:
   - The main loading happens in `apply_styles()` in `gui/styles/__init__.py`
   - But there's also a separate loading in `MainWindow._apply_stylesheet()`

## Recommended Approach

Based on the analysis, the property-based styling approach is the most maintainable:

```css
/* In theme.qss - using variables */
QPushButton[type="primary"] {
    background-color: var(--color-primary);
    color: var(--color-text-primary);
}

/* Instead of object names in style.qss */
#specific_button {
    background-color: #3B8A45;
    color: white;
}
```

## Style Loading Flow

1. **Initial Application Load**:
   ```python
   # In main.py
   from src.fm.gui.styles import apply_styles
   apply_styles(app)
   ```

2. **Style Combination**:
   ```python
   # In gui/styles/__init__.py
   with open(styles_dir / "theme.qss", 'r') as f:
       theme = f.read()
   with open(styles_dir / "style.qss", 'r') as f:
       style = f.read()
   combined = theme + "\n" + style
   ```

3. **Dynamic Updates**:
   - Font size updates are supported but not fully implemented
   - Theme switching is planned but not implemented

## Recommendations for Improvement

1. **Consolidate Styling Approach**:
   - Move all styling to use property-based approach
   - Eliminate duplicate rules between theme.qss and style.qss
   - Consider merging theme.qss and style.qss into a single file

2. **Centralize Style Loading**:
   - Remove the separate stylesheet loading in MainWindow
   - Use only the central apply_styles() function

3. **Improve Variable Usage**:
   - Replace all hardcoded colors with variables from palette.qss
   - Define semantic variables for spacing, borders, etc.

4. **Documentation**:
   - Document which approach (property, class, or object name) should be used when
   - Create examples for each widget type

## Conclusion

The current styling system is in transition between an older approach (hardcoded values, object names) and a newer, more maintainable approach (variables, property-based styling). The property-based approach with CSS variables from palette.qss is the most maintainable and should be standardized across the application.
