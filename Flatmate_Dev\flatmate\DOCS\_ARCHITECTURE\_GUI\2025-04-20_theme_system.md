# Theme System Architecture

## Current Implementation

The FlatMate application currently uses a static theme system with the following components:

- **Location**: All styling is stored in `src/fm/gui/styles/`
  - `style.qss`: Main Qt stylesheet with dynamic font size support
  - `components.py`: Component-specific styles
  - `constants.py`: Style constants (colors, sizes, etc.)
  - `theme.py`: Theme-related utilities

- **Loading**: The main stylesheet is loaded directly in `main.py` during application initialization
  ```python
  style_path = Path(__file__).parent / 'src' / 'fm' / 'gui' / 'styles' / 'style.qss'
  with open(style_path) as f:
      font_size = config.get_value('ui.font_size', 14)
      style = f.read() % (font_size, font_size, font_size)
      app.setStyleSheet(style)
  ```

## Future Theme System Proposal

### 1. Theme Structure
- Create multiple theme files in `src/fm/gui/styles/themes/`
  ```
  themes/
  ├── dark.qss
  ├── light.qss
  ├── high_contrast.qss
  └── theme_data.py  # Theme metadata and utilities
  ```

### 2. User Preferences
Add theme-related preferences to the configuration system:
```python
# In ConfigKeys
class Window(Enum):
    THEME = 'ui.theme'  # Selected theme name
    FONT_SIZE = 'ui.font_size'  # Font size preference
    HIGH_CONTRAST = 'ui.high_contrast'  # Accessibility option

# Default values
defaults = {
    Window.THEME: 'light',
    Window.FONT_SIZE: 14,
    Window.HIGH_CONTRAST: False
}
```

### 3. Theme Manager
Create a dedicated ThemeManager class to handle:
- Theme loading and switching
- Dynamic theme updates
- Theme-specific color schemes
- Accessibility features

### 4. Implementation Plan

1. **Phase 1: Theme Infrastructure**
   - Create theme directory structure
   - Split current style.qss into light/dark variants
   - Implement ThemeManager class

2. **Phase 2: User Controls**
   - Add theme selection to preferences dialog
   - Create theme preview in settings
   - Add keyboard shortcuts for theme switching

3. **Phase 3: Advanced Features**
   - System theme detection and auto-switching
   - Custom theme creation/import
   - High contrast and accessibility modes
   - Dynamic theme updates without app restart

### 5. Benefits

1. **Customization**: Users can choose their preferred theme
2. **Accessibility**: Support for various visual needs
3. **Maintainability**: Organized theme structure
4. **Extensibility**: Easy to add new themes
5. **User Experience**: Consistent styling across the application

### 6. Technical Considerations

1. **Performance**
   - Cache compiled stylesheets
   - Lazy load unused themes
   - Optimize theme switching

2. **Testing**
   - Visual regression tests for each theme
   - Accessibility compliance checks
   - Theme switching stress tests

3. **Migration**
   - Maintain backward compatibility
   - Provide theme migration utilities
   - Document theme creation guidelines

### 7. Future Possibilities

1. **Theme Marketplace**
   - Allow community theme sharing
   - Theme rating and reviews
   - Version control for themes

2. **Advanced Customization**
   - Per-module theme overrides
   - Dynamic color schemes
   - Custom font support

3. **Integration**
   - System theme integration
   - Time-based theme switching
   - API for theme-aware plugins
