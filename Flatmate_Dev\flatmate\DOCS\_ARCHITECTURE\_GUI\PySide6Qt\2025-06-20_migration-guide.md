# Migration Guide: PyQt-Frameless-Window Integration

## Overview

This guide provides step-by-step instructions for migrating the Flatmate application from custom frameless window implementation to PyQt-Frameless-Window library.

## Current State Analysis

### Issues with Current Implementation
- **3-click maximize/restore problem**: Takes multiple clicks to properly toggle window state
- **State synchronization issues**: Button icons don't match actual window state
- **Event timing problems**: `WindowStateChange` events fire at inconsistent times
- **Complex debugging code**: Extensive logging and timeout mechanisms needed

### Files Affected
- `fm/gui/main_window.py` - Main window class
- `fm/gui/_main_window_components/title_bar/custom_title_bar.py` - Custom title bar
- `fm/gui/_main_window_components/title_bar/components/window_controls.py` - Window controls

## Migration Steps

### Step 1: Install Library

```bash
# Activate virtual environment
source .venv_fm313/bin/activate  # Linux/macOS
# or
.venv_fm313\Scripts\activate     # Windows

# Install library
pip install PySide6-Frameless-Window
```

### Step 2: Create Test Branch

```bash
git checkout -b feature/frameless-window-library
git add .
git commit -m "Backup before PyQt-Frameless-Window migration"
```

### Step 3: Update Main Window Base Class

**File**: `fm/gui/main_window.py`

```python
# Add import
from qframelesswindow import FramelessWindow

# Change base class
class MainWindow(FramelessWindow):  # Changed from QMainWindow
    def __init__(self):
        super().__init__()
        # Rest of initialization remains the same
```

### Step 4: Remove Custom Window State Management

**Remove from `main_window.py`**:
```python
# Remove these imports
from PySide6.QtGui import QWindowStateChangeEvent

# Remove these methods
def changeEvent(self, event):
    # Remove entire method

def _update_window_controls_state(self):
    # Remove entire method

def _initialize_window_controls_state(self):
    # Remove entire method
```

### Step 5: Simplify Window Controls

**File**: `fm/gui/_main_window_components/title_bar/components/window_controls.py`

**Remove complex state management**:
```python
# Remove these attributes from __init__
self._pending_action = None
self._timeout_timer = QTimer()

# Simplify _toggle_maximize method
def _toggle_maximize(self):
    """Toggle between maximized and normal window state."""
    if self.parent.isMaximized():
        self.parent.showNormal()
    else:
        self.parent.showMaximized()
    # Library handles state updates automatically

# Remove these methods entirely
def _on_maximize_clicked(self):
    # Remove - use direct connection

def _handle_timeout(self):
    # Remove - no longer needed

def update_for_state(self, maximized: bool):
    # Simplify or remove - library may handle this
```

### Step 6: Update Signal Connections

**Simplify signal connections**:
```python
def _connect_signals(self):
    """Connect button signals to window actions."""
    self.minimize_btn.clicked.connect(lambda: self.parent.showMinimized())
    self.maximize_btn.clicked.connect(self._toggle_maximize)  # Direct connection
    self.close_btn.clicked.connect(lambda: self.parent.close())
```

### Step 7: Test Basic Functionality

**Create test script**:
```python
# test_frameless.py
import sys
from PySide6.QtWidgets import QApplication
from fm.gui.main_window import MainWindow

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    
    print("Testing maximize/restore functionality...")
    print("Click the maximize button and verify single-click operation")
    
    app.exec()
```

### Step 8: Optional - Custom Title Bar Integration

If you want to preserve the exact current appearance:

```python
from qframelesswindow import FramelessWindow, StandardTitleBar

class FlatmateTitleBar(StandardTitleBar):
    """Custom title bar that matches Flatmate design"""
    
    def __init__(self, parent):
        super().__init__(parent)
        
        # Apply Flatmate styling
        self.setStyleSheet("""
            StandardTitleBar {
                background-color: #2b2b2b;
                color: white;
                border-bottom: 1px solid #3d3d3d;
            }
        """)
        
        # Customize buttons to match current design
        self.minBtn.setStyleSheet("/* Your current button styles */")
        self.maxBtn.setStyleSheet("/* Your current button styles */")
        self.closeBtn.setStyleSheet("/* Your current button styles */")

class MainWindow(FramelessWindow):
    def __init__(self):
        super().__init__()
        
        # Replace default title bar with custom one
        self.setTitleBar(FlatmateTitleBar(self))
        
        # Continue with rest of setup
        self.setup_ui()
```

### Step 9: Remove Debug Code

**Remove all debugging code**:
```python
# Remove all print statements like:
print(f"DEBUG: [{timestamp:.3f}] ...")

# Remove timing imports:
import time
timestamp = time.time()

# Remove timeout mechanisms
# Remove state tracking variables
# Remove complex event handling
```

### Step 10: Update Requirements

**Add to requirements.txt**:
```
PySide6-Frameless-Window>=0.3.0
```

## Testing Checklist

### Functional Testing
- [ ] Application starts without errors
- [ ] Window displays correctly
- [ ] Title bar appears properly
- [ ] Minimize button works
- [ ] **Maximize button works with single click**
- [ ] **Restore button works with single click**
- [ ] Close button works
- [ ] Window can be dragged by title bar
- [ ] Window can be resized from edges

### Regression Testing
- [ ] All existing functionality works
- [ ] Module navigation works
- [ ] UI components display correctly
- [ ] Application performance is maintained
- [ ] Memory usage is reasonable

### Cross-Platform Testing
- [ ] Windows 10/11
- [ ] Linux (if applicable)
- [ ] macOS (if applicable)

## Rollback Plan

If issues arise during migration:

### Quick Rollback
```bash
git checkout main
git branch -D feature/frameless-window-library
```

### Partial Rollback
```bash
# Keep changes but revert specific files
git checkout HEAD -- fm/gui/main_window.py
git checkout HEAD -- fm/gui/_main_window_components/title_bar/components/window_controls.py
```

## Expected Benefits

### Immediate Benefits
- ✅ **Single-click maximize/restore** - No more 3-click problem
- ✅ **Reliable state synchronization** - Button icons always match window state
- ✅ **Simplified code** - Remove complex event handling and debugging code
- ✅ **Better maintainability** - Less custom code to maintain

### Long-term Benefits
- ✅ **Cross-platform consistency** - Reliable behavior across all platforms
- ✅ **Future-proof** - Library handles Qt updates and edge cases
- ✅ **Professional appearance** - Battle-tested window management
- ✅ **Reduced debugging** - No more window state synchronization issues

## Troubleshooting

### Common Issues

**1. Import Error**
```
ModuleNotFoundError: No module named 'qframelesswindow'
```
**Solution**: Ensure library is installed in correct virtual environment

**2. Window Doesn't Display**
```python
# Check if window is created properly
window = MainWindow()
print(f"Window created: {window}")
window.show()
```

**3. Title Bar Missing**
```python
# Verify title bar is set
if hasattr(window, 'titleBar'):
    print("Title bar exists")
else:
    print("Title bar missing - check setTitleBar() call")
```

**4. Styling Issues**
```python
# Apply styles after library initialization
self.setTitleBar(custom_title_bar)
# Then apply additional styles
self.setStyleSheet("/* additional styles */")
```

## Success Criteria

The migration is successful when:

1. **Single-click operation**: Maximize and restore work with one click each
2. **No debug output**: All debugging print statements removed
3. **Simplified code**: Complex state management code removed
4. **Maintained functionality**: All existing features work as before
5. **Improved reliability**: No more window state synchronization issues

## Next Steps After Migration

1. **Code cleanup**: Remove unused imports and methods
2. **Documentation update**: Update code comments and documentation
3. **Performance testing**: Verify no performance regression
4. **User testing**: Get feedback on improved window behavior
5. **Deployment**: Test in production-like environment

## Support Resources

- [PyQt-Frameless-Window Documentation](https://github.com/zhiyiYo/PyQt-Frameless-Window/tree/PySide6)
- [Example Implementation](https://github.com/zhiyiYo/PyQt-Frameless-Window/blob/PySide6/examples/demo.py)
- [Qt Forum Discussions](https://forum.qt.io/topic/151702/title-bar-customization)

## Contact

If you encounter issues during migration, refer to:
- This documentation in `docs/PySide6Qt/`
- Git commit history for changes made
- Library documentation and examples
