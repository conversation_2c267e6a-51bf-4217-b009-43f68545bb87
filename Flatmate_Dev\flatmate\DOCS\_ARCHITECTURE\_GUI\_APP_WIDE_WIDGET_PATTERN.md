# App-Wide Custom Widget Pattern
*last updated 2025-06-20*

## Overview
This document defines the standard pattern for all custom widgets in the FlatMate application. This pattern emerged from the table view redesign and should be applied consistently across all custom UI components.

## Core Problem Solved
- **Inconsistent widget APIs** - Different widgets had different configuration methods
- **Complex initialization** - Unclear order of operations and configuration
- **No runtime flexibility** - Difficult to change widget behavior after setup
- **Poor discoverability** - Configuration options scattered and hard to find

## Design Principles

### 1. Separation of Concerns
- **Configuration** sets instance runtime defaults (behavior and appearance)
- **Content** provides the data/widgets to display
- **Visibility** controls when and how the widget is shown
- **Dynamic methods** allow runtime changes from user interaction
- >> lifecycle management ?

### 2. Consistent API Pattern
All custom widgets follow the same method structure and naming conventions.

### 3. Flexible Initialization Order
The order of configure → set_content → show should be flexible where possible.

### 4. Chainable Methods
All configuration and dynamic methods return `self` for fluent interface.

## Standard Widget Structure

### Core Pattern Template
```python
class CustomWidget(QWidget):
    """Standard pattern for all custom widgets."""
    
    def __init__(self, parent=None):
        """Initialize with sensible defaults."""
        super().__init__(parent)
        self._config = WidgetConfig()  # Instance runtime defaults
        self._content = None
        self._is_shown = False
        self._setup_ui()
    
    # === CONFIGURATION (Instance Runtime Defaults) ===
    def configure(self, **kwargs) -> 'CustomWidget':
        """Configure widget behavior and appearance.
        
        This sets the baseline behavior for this widget instance.
        All kwargs should have sensible defaults.
        
        Returns:
            self for method chaining
        """
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                setattr(self._config, key, value)
            else:
                raise ValueError(f"Unknown configuration option: {key}")
        
        # Apply configuration if already shown
        if self._is_shown:
            self._apply_configuration()
        return self
    
    # === CONTENT MANAGEMENT ===
    def set_content(self, content) -> 'CustomWidget':
        """Set the widget content.
        
        Args:
            content: The content to display (varies by widget type)
            
        Returns:
            self for method chaining
        """
        self._content = content
        if self._is_shown:
            self._apply_content()
        return self
    
    # === VISIBILITY CONTROL ===
    def show(self) -> 'CustomWidget':
        """Show widget and apply all configuration.
        
        This is the trigger that makes everything visible and applies
        all pending configuration and content.
        
        Returns:
            self for method chaining
        """
        self._is_shown = True
        if self._content is not None:
            self._apply_configuration()
            self._apply_content()
        super().show()
        return self
    
    def hide(self) -> 'CustomWidget':
        """Hide widget.
        
        Returns:
            self for method chaining
        """
        self._is_shown = False
        super().hide()
        return self
    
    # === DYNAMIC RUNTIME METHODS ===
    def set_property(self, value) -> 'CustomWidget':
        """Change specific property at runtime.
        
        These methods update both the UI state AND the internal 
        configuration so changes persist through refreshes.
        
        Returns:
            self for method chaining
        """
        self._config.property = value
        if self._is_shown:
            self._apply_property_change(value)
        return self
    
    # === INTERNAL METHODS ===
    def _setup_ui(self):
        """Set up the UI components. Called once in __init__."""
        pass
    
    def _apply_configuration(self):
        """Apply current configuration to UI. Called when shown or config changes."""
        pass
    
    def _apply_content(self):
        """Apply current content to UI. Called when shown or content changes."""
        pass
```

## Naming Conventions

### Configuration Methods
- **`configure(**kwargs)`** - Sets instance runtime defaults
- Always returns `self` for chaining
- Uses kwargs for flexibility and discoverability

### Content Methods (Widget-Specific)
- **`set_content(content)`** - Generic content setting
- **`set_dataframe(df)`** - For table/data widgets
- **`set_text(text)`** - For text-based widgets
- **`set_items(items)`** - For list/collection widgets
- **`set_actions(actions)`** - For toolbar/menu widgets
- Always returns `self` for chaining

### Visibility Methods
- **`show()`** - Show and apply all configuration
- **`hide()`** - Hide widget
- Always returns `self` for chaining

### Dynamic Runtime Methods
- **`set_[property](value)`** - Change specific property
- **`hide_[element]()`** - Hide specific sub-element
- **`show_[element]()`** - Show specific sub-element  
- **`toggle_[element]()`** - Toggle specific sub-element
- **`get_[property]()`** - Get current property value
- Always returns `self` for chaining (except getters)

## Configuration Strategy

### Hierarchical Defaults (Recommended)
```python
@dataclass
class WidgetConfig:
    """Widget configuration with hierarchical defaults."""
    
    # App-wide defaults (inherited)
    theme: str = field(default_factory=lambda: AppDefaults.THEME)
    font_size: int = field(default_factory=lambda: AppDefaults.FONT_SIZE)
    
    # Widget-specific defaults (explicit)
    show_border: bool = True
    padding: int = 4
    orientation: str = 'horizontal'
    
    # Module-specific (set via configure())
    custom_property: Optional[str] = None
```

### Benefits:
- **App consistency** for common properties (theme, fonts)
- **Widget autonomy** for specific behavior
- **Module control** for custom requirements
- **Clear hierarchy** - app → widget → module

## Usage Examples

### Table View Widget
```python
# Setup
table = CustomTableView()
table.configure(
    auto_size_columns=True,
    max_column_width=40,
    show_toolbar=True,
    editable_columns=['tags']
)
table.set_dataframe(df)
table.show()

# Runtime changes
table.hide_columns(['balance'])
table.resize_column('details', 60)
table.hide_toolbar()
```

### Toolbar Widget
```python
# Setup
toolbar = CustomToolbar()
toolbar.configure(
    orientation='horizontal',
    icon_size=16,
    show_groups=['filter', 'export']
)
toolbar.set_actions(action_list)
toolbar.show()

# Runtime changes
toolbar.hide_groups(['export'])
toolbar.set_icon_size(20)
toolbar.toggle_group('column')
```

### Panel Widget
```python
# Setup
panel = CustomPanel()
panel.configure(
    collapsible=True,
    title="Details",
    default_collapsed=False
)
panel.set_content(detail_widget)
panel.show()

# Runtime changes
panel.collapse()
panel.set_title("Updated Details")
panel.toggle_border()
```

## Implementation Guidelines

### 1. Top-Level Parent Responsibility
- **Layout management** - Arranges child components
- **Configuration delegation** - Passes config to children
- **Sensible defaults** - Provides good out-of-box experience
- **Method coordination** - Ensures proper initialization order

### 2. Configuration Design
- **Kwargs-based** for flexibility and IDE support
- **Type hints** for all parameters
- **Validation** of configuration options
- **Documentation** of all options with examples

### 3. Content Handling
- **Flexible types** - Accept various content formats
- **Validation** - Ensure content is appropriate
- **Transformation** - Convert content to internal format
- **Error handling** - Graceful handling of invalid content

### 4. Dynamic Method Design
- **Update config** - Change internal configuration
- **Update UI** - Apply change to visible elements
- **Emit signals** - Notify other components if needed
- **Return self** - Enable method chaining

### 5. Error Handling
- **Validation errors** - Clear messages for invalid config
- **State errors** - Handle calls in wrong order gracefully
- **Content errors** - Helpful messages for invalid content
- **Recovery** - Ability to reset to known good state

## Benefits of This Pattern

### For Developers
- **Predictable APIs** - All widgets work the same way
- **Easy discovery** - Standard method names and patterns
- **Flexible usage** - Multiple ways to achieve the same result
- **Good tooling** - IDE autocomplete and type checking

### For Maintainability
- **Consistent code** - Same patterns across all widgets
- **Easy testing** - Standard test patterns for all widgets
- **Clear separation** - Configuration vs content vs behavior
- **Extensible** - Easy to add new features consistently

### For Users
- **Intuitive behavior** - Widgets behave predictably
- **Runtime flexibility** - Can adjust widgets during use
- **Responsive UI** - Changes apply immediately
- **Consistent experience** - All widgets feel familiar

## Migration Strategy

### Phase 1: Document and Standardize
1. Document current widget APIs and identify inconsistencies
2. Create standard base classes and mixins
3. Establish naming conventions and patterns

### Phase 2: Implement New Widgets
1. Apply pattern to new widgets (like redesigned table view)
2. Create examples and documentation
3. Gather feedback and refine pattern

### Phase 3: Migrate Existing Widgets
1. Update existing widgets to follow pattern
2. Maintain backward compatibility where possible
3. Update calling code to use new APIs

### Phase 4: Cleanup
1. Remove old inconsistent APIs
2. Consolidate common functionality
3. Update documentation and examples

## Related Documents
- [Table View Redesign Discussion](./TABLE_VIEW_REDESIGN_DISCUSSION.md) - Detailed application of this pattern to table views
- [Table View Initialization Sequence](./TABLE_VIEW_INIT_SEQUENCE.md) - Current problems this pattern solves



- should we have a custom element base class or mix-in
- 