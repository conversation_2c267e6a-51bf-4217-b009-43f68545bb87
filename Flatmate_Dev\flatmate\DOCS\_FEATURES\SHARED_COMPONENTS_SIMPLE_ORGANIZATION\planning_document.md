# Shared Components Simple Organization Plan

**Date**: July 22, 2025  
**Status**: Planning Phase  
**Goal**: Organize shared components logically without breaking anything that fucking works  

## Problem Statement

The current shared components work perfectly but are disorganized. Previous refactoring attempts added unnecessary complexity (BaseWidget, configs, wrappers) that broke styling and made simple things complicated.

**What we need**: A fucking checkbox with a label that works anywhere in the app, with consistent styling.

**What we don't need**: Abstract base classes, configuration systems, factory patterns, or any other architectural masturbation.

## Current Working Reality ✅

### Components That Work
```python
# Buttons - Simple QPushButton inheritance + setProperty
ActionButton("Process")           → setProperty("type", "action_btn")
SecondaryButton("Select...")      → setProperty("type", "select_btn") 
ExitButton("Cancel")              → setProperty("type", "exit_btn")

# Checkboxes - Simple QWidget + QCheckBox + QLabel
LabeledCheckBox("Update Database", checked=True)

# Option Menus - Simple QWidget + QComboBox combinations
OptionMenuWithLabel(label="Source:", options=["CSV", "Bank"])
OptionMenuWithLabelAndButton(label="Account:", button_text="Select...")
```

### Label Patterns Currently Used
```python
# Main titles
self.title = QLabel("Update Data")
self.title.setObjectName("heading")

# Section headings  
self.source_label = QLabel("1. Source Files")
self.source_label.setObjectName("lbl_panel_subheading")

# Info labels
self.file_info_label = QLabel("File:")
self.file_info_label.setObjectName("subheading")
```

### CSS Styling That Works
```css
QPushButton[type="action_btn"] { background-color: #3B8A45; }
QPushButton[type="select_btn"] { background-color: #3B7443; }
QPushButton[type="exit_btn"] { background-color: #2E5A35; }

QLabel#heading { font-size: 1.5em; font-weight: bold; }
QLabel#lbl_panel_subheading { color: #CCCCCC; font-size: 1.3em; }
QLabel#subheading { color: #CCCCCC; font-weight: bold; }
```

## Proposed Simple Organization

### File Structure
```
flatmate/src/fm/gui/_shared_components/widgets/
├── __init__.py                    # Convenience imports
├── buttons.py                     # ActionButton, SecondaryButton, ExitButton
├── checkboxes.py                  # LabeledCheckBox  
├── labels.py                      # HeadingLabel, SubheadingLabel, InfoLabel
├── option_menus.py                # OptionMenuWithLabel, OptionMenuWithLabelAndButton
├── account_selector.py            # AccountSelector
└── date_filter_pane.py            # DateFilterPane
```

### New Labels Component (labels.py)
```python
"""
Simple label components that just wrap QLabel + setObjectName.
No bullshit, no complexity, just consistent styling.
"""
from PySide6.QtWidgets import QLabel

class HeadingLabel(QLabel):
    """Main heading label - just QLabel + objectName='heading'."""
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("heading")

class SubheadingLabel(QLabel):
    """Panel subheading label - just QLabel + objectName='lbl_panel_subheading'."""
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("lbl_panel_subheading")

class InfoLabel(QLabel):
    """Info label - just QLabel + objectName='subheading'."""
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("subheading")
```

### Updated __init__.py
```python
"""
Shared widget components - simple, working, no bullshit.
"""
from .buttons import ActionButton, SecondaryButton, ExitButton
from .checkboxes import LabeledCheckBox
from .labels import HeadingLabel, SubheadingLabel, InfoLabel
from .option_menus import OptionMenuWithLabel, OptionMenuWithLabelAndButton
from .account_selector import AccountSelector
from .date_filter_pane import DateFilterPane

__all__ = [
    # Buttons
    'ActionButton', 'SecondaryButton', 'ExitButton',
    # Checkboxes  
    'LabeledCheckBox',
    # Labels
    'HeadingLabel', 'SubheadingLabel', 'InfoLabel',
    # Option Menus
    'OptionMenuWithLabel', 'OptionMenuWithLabelAndButton',
    # Other Components
    'AccountSelector', 'DateFilterPane'
]
```

## Implementation Plan

### Phase 1: Create Labels Component
1. Create `labels.py` with simple label classes
2. Update `__init__.py` to include label imports
3. Test that imports work

### Phase 2: Update Module Usage
1. **Update Data Module**: Replace direct QLabel usage with shared components
2. **Categorize Module**: Replace direct QLabel usage with shared components  
3. **Home Module**: Replace direct QLabel usage with shared components

### Phase 3: Validation
1. Run app and verify styling works
2. Test all modules load correctly
3. Verify buttons still have proper colors (not white)

## Usage Examples

### Before (Current)
```python
# Repetitive, inconsistent
self.title = QLabel("Update Data")
self.title.setObjectName("heading")

self.source_label = QLabel("1. Source Files")
self.source_label.setObjectName("lbl_panel_subheading")

self.file_info_label = QLabel("File:")
self.file_info_label.setObjectName("subheading")
```

### After (Simple & Consistent)
```python
from fm.gui._shared_components.widgets import HeadingLabel, SubheadingLabel, InfoLabel

# Clean, obvious, consistent
self.title = HeadingLabel("Update Data")
self.source_label = SubheadingLabel("1. Source Files")  
self.file_info_label = InfoLabel("File:")
```

## Benefits

✅ **Consistent styling** - All labels use the same CSS classes  
✅ **Simple calling code** - One line instead of two  
✅ **Easy to use anywhere** - Import and use, no configuration needed  
✅ **Maintains existing functionality** - Just wraps QLabel + setObjectName  
✅ **No breaking changes** - Existing code continues to work  
✅ **No architectural complexity** - Simple inheritance, no configs or wrappers  

## What We're NOT Doing

❌ **BaseWidget abstractions** - Unnecessary complexity  
❌ **Configuration systems** - Over-engineering  
❌ **Factory patterns** - Solving problems we don't have  
❌ **Wrapper widgets** - Breaks CSS selectors  
❌ **Abstract base classes** - Creates more problems than it solves  

## Success Criteria

1. **App runs without errors** after implementation
2. **Buttons have proper colors** (green, not white)
3. **Labels have consistent styling** across all modules
4. **Import statements are clean** and obvious
5. **Code is simpler** than before, not more complex

---

**Next Steps**: Implement Phase 1 - Create the simple labels component and test imports.
