# Protocol: Scoping & Planning Session

**Version**: 1.0  
**Status**: Active  
**Author**: Cascade & Quinn  

---

## 1. Objective

To investigate a task, define its scope, and produce a detailed, step-by-step implementation plan. **No production code is written or modified during this phase.**

The final, approved plan is the primary deliverable and serves as the official blueprint for a subsequent `REFACTOR` or `FEATURE` work session. This protocol ensures that all implementation work is based on a solid, pre-approved foundation.

## 2. Compatibility

This protocol is designed to be fully compatible with the main `/work-session` system and the `unified-work-session.md` workflow. It acts as a "Phase 0" before any implementation-focused session begins.

## 3. Session Workflow

### Step 3.1: Session Setup

Follows the standard `/work-session` setup procedure with a specific naming convention:

*   **Create Session Directory:**
    ```bash
    # Example for refactoring the update_data UI
    mkdir "flatmate/DOCS/_FEATURES/PLANNING_update_data_ui_refactor"
    ```

*   **Create Session Log:**
    - Copy the `SESSION_LOG_TEMPLATE.md` into the new directory. This log will track the investigation, evidence, and decision-making process.

*   **Create Subdirectories:**
    ```bash
    cd "flatmate/DOCS/_FEATURES/PLANNING_<feature_or_refactor_name>"
    mkdir "EVIDENCE" "FINAL_PLAN"
    ```
    - `EVIDENCE`: Stores all artifacts gathered during the investigation (code snippets, search results, screenshots, notes).
    - `FINAL_PLAN`: Contains the final, deliverable plan document.

### Step 3.2: Investigation & Analysis (The "Work")

This phase uses investigative tools to understand the problem space thoroughly. All actions and findings must be logged in `SESSION_LOG.md`.

*   **Code Exploration:**
    - Use tools like `grep_search`, `view_file_outline`, `list_dir`, and `codebase_search` to analyze relevant parts of the codebase.
*   **Documentation Review:**
    - Read existing project documentation, previous reports, architectural guides, and relevant protocols.
*   **Evidence Collection:**
    - Save key findings (e.g., `grep_results.txt`, `file_outline.txt`, relevant code snippets, analysis notes) into the `EVIDENCE` directory.

### Step 3.3: Plan Formulation (The Deliverable)

This is the synthesis step where investigation turns into a concrete plan.

*   **Synthesize Findings:**
    - Review all evidence and notes from the `SESSION_LOG.md`.
*   **Draft the Plan:**
    - Create a formal, actionable plan document (e.g., `refactor_plan_v1.md`).
    - The plan must be clear, specific, and detailed enough for another developer or AI to execute without ambiguity. It should include:
        - A clear objective.
        - A list of all files to be modified.
        - Step-by-step implementation instructions, including specific code changes where possible.
        - A validation or testing plan to verify success.
*   **Finalize the Plan:**
    - Place the final document in the `FINAL_PLAN` directory.

### Step 3.4: Phase Completion & Review

The session is complete once the plan is finalized and all supporting evidence is logged and stored.

The plan is then submitted for review and approval.

Once approved, the `FINAL_PLAN` becomes the official input for a new `REFACTOR` or `FEATURE` work session, which can then proceed to the "touch code" phase.

---
