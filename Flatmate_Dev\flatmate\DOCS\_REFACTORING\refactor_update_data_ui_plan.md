# Refactoring Plan: Update Data Module UI

**Date**: July 23, 2025  
**Status**: Proposed

## 1. Objective

To refactor the `update_data` module's UI to use standardized shared components, specifically replacing manual `<PERSON>Label` and `QComboBox` pairs with the `OptionMenuWithLabel` convenience widget. This will improve code quality, reduce duplication, and align the module with project-wide GUI architecture standards.

## 2. Target File

- `src/fm/modules/update_data/_view/left_panel/widgets/widgets.py`

## 3. Pre-requisites

- The `OptionMenuWithLabel` widget must be available for import from `fm.gui._shared_components.widgets.option_menus`.
- The developer/agent must have read and understood the `WIDGET_CREATION_GUIDE.md`.

## 4. Step-by-Step Implementation Plan

### Step 1: Modify Imports

In `widgets.py`, add the following import:

```python
from fm.gui._shared_components.widgets.option_menus import OptionMenuWithLabel
```

### Step 2: Refactor `LeftPanelButtonsWidget._init_ui`

This is the core of the refactor. The following changes must be made within this method:

1.  **Remove Source Widget Creation**:
    Delete these lines:
    ```python
    self.source_label = QLabel("1. Source Files")
    self.source_combo = QComboBox()
    self.source_combo.setSizeAdjustPolicy(
        QComboBox.SizeAdjustPolicy.AdjustToContents
    )
    ```

2.  **Instantiate `OptionMenuWithLabel` for Source**:
    Add this line in its place:
    ```python
    self.source_menu = OptionMenuWithLabel("1. Source Files", options=[])
    ```
    *Note: The initial options list is empty; it will be populated dynamically by the controller.* 

3.  **Remove Save Widget Creation**:
    Delete these lines:
    ```python
    self.save_label = QLabel("2. Save Location")
    self.save_combo = QComboBox()
    self.save_combo.setSizeAdjustPolicy(
        QComboBox.SizeAdjustPolicy.AdjustToContents
    )
    ```

4.  **Instantiate `OptionMenuWithLabel` for Save**:
    Add this line in its place:
    ```python
    self.save_menu = OptionMenuWithLabel("2. Save Location", options=[])
    ```

### Step 3: Update Layout

The layout code must be adjusted to add the new compound widgets instead of the separate labels and combo boxes.

-   **Find** the section where `source_label` and `source_combo` are added to the layout.
-   **Replace** those `layout.addWidget()` calls with a single call:
    ```python
    layout.addWidget(self.source_menu)
    ```
-   **Repeat** for the save location, replacing the `save_label` and `save_combo` additions with:
    ```python
    layout.addWidget(self.save_menu)
    ```

### Step 4: Update Signal Connections and Logic

Any code that previously referenced `self.source_combo` or `self.save_combo` must be updated.

-   Search the class for any usage of `self.source_combo` and `self.save_combo` (e.g., connecting to `currentIndexChanged` or calling `addItems`).
-   Update these references to point to the `QComboBox` instance inside the new widget, like so:
    -   `self.source_combo` becomes `self.source_menu.combo_box`
    -   `self.save_combo` becomes `self.save_menu.combo_box`

## 5. Validation

After implementation, run the application and navigate to the "Update Data" module.

-   **Verify** that the "Source Files" and "Save Location" dropdowns appear and are styled correctly.
-   **Confirm** that they are populated with data and function as they did before the refactor.
-   **Ensure** no functionality has been broken.
