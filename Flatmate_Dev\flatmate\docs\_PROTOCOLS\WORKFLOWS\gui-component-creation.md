---
description: "A simplified workflow for creating GUI components for the Flatmate project."
type: "specialized_workflow"
version: "2.0"
agents: ["cascade", "windsurf"]
integration: "Uses unified-work-session.md for session management"
---

# GUI Component Creation Protocol (Simplified)

**Purpose**: To provide a clear, simple, and safe workflow for creating new GUI components that are consistent with the Flatmate project's architecture.

**Core Principle**: This protocol is designed to prevent the over-engineering that led to past failures. It prioritizes simplicity, maintainability, and adherence to our established styling patterns.

--- 

## The Golden Rule: Follow the Guide

All GUI component creation **must** follow the detailed steps and patterns outlined in the official guide:

**Single Source of Truth**: [`WIDGET_CREATION_GUIDE.md`](../../_ARCHITECTURE/GUI/WIDGET_CREATION_GUIDE.md)

Before starting, every developer and AI assistant must read and understand the guide. It contains the correct, project-specific patterns for creating simple and compound widgets.

--- 

## Workflow Steps

### 1. Pre-Analysis

- **Check for existing components**: Before creating a new widget, search `fm/gui/_shared_components/widgets/` to see if a similar component already exists.
- **Consult the guide**: Open and read the [`WIDGET_CREATION_GUIDE.md`](../../architecture/gui/WIDGET_CREATION_GUIDE.md). This is not optional.

### 2. Design & Implementation

- **Choose the correct pattern**: Based on the guide, decide whether to use **Composition** (inheriting `QWidget`) or **Simple Inheritance** (inheriting `QPushButton`, `QLabel`, etc.).
- **Implement the widget**: Create the `.py` file in the appropriate directory.
- **Ensure it is stylable**: Use `setObjectName()` or `setProperty()` to hook into the existing QSS stylesheet (`fm/gui/styles/theme.qss`).

### 3. Integration & Validation

- **Update `__init__.py`**: Add the new component to the `__all__` list in the appropriate `widgets` package for easy importing.
- **Test**: Run the application and visually confirm that the new component works as expected and is styled correctly.

---

## What This Protocol Replaces

This simplified protocol intentionally supersedes previous, more complex workflows. We **explicitly forbid** the following patterns:

- **NO** abstract `BaseWidget` classes.
- **NO** `Config` objects for styling or setup.
- **NO** wrapper widgets that break QSS styling.
- **NO** factory patterns.

Adherence to the `WIDGET_CREATION_GUIDE.md` ensures these anti-patterns are avoided.
