"""
Shared widget components for the Flatmate application.

This package provides reusable concrete UI widgets that can be used
across different modules for consistent styling and behavior.

Organized by widget type:
- buttons: ActionButton, SecondaryButton, ExitButton
- checkboxes: LabeledCheckBox
- labels: HeadingLabel, SubheadingLabel, InfoLabel
- option_menus: OptionMenuWithLabel, OptionMenuWithLabelAndButton
- selectors: AccountSelector
- filters: DateFilterPane
"""

from .buttons import ActionButton, SecondaryButton, ExitButton
from .checkboxes import LabeledCheckBox
from .labels import HeadingLabel, SubheadingLabel, InfoLabel
from .option_menus import OptionMenuWithLabel, OptionMenuWithLabelAndButton
from .account_selector import AccountSelector
from .date_filter_pane import DateFilterPane

__all__ = [
    # Buttons
    'ActionButton',
    'SecondaryButton',
    'ExitButton',

    # Checkboxes
    'LabeledCheckBox',

    # Labels
    'HeadingLabel',
    'SubheadingLabel',
    'InfoLabel',

    # Option Menus
    'OptionMenuWithLabel',
    'OptionMenuWithLabelAndButton',

    # Selectors
    'AccountSelector',

    # Filters
    'DateFilterPane'
]
