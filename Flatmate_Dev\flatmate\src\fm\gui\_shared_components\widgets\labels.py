"""
Simple label components that just wrap QLabel + setObjectName.
No bullshit, no complexity, just consistent styling.
"""
from PySide6.QtWidgets import QLabel


class HeadingLabel(QLabel):
    """Main heading label - just QLabel + objectName='heading'."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("heading")


class SubheadingLabel(QLabel):
    """Panel subheading label - just QLabel + objectName='lbl_panel_subheading'."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("lbl_panel_subheading")


class InfoLabel(QLabel):
    """Info label - just QLabel + objectName='subheading'."""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setObjectName("subheading")
