"""
Shared widget components for the Flatmate application.

This package provides reusable concrete UI widgets that can be used
across different modules for consistent styling and behavior.

Enhanced hierarchical organization following App-Wide Widget Pattern:
- base: BaseWidget foundation class
- buttons: ActionButton, SecondaryButton, ExitButton
- checkboxes: LabeledCheckBox
- option_menus: OptionMenuWithLabel, OptionMenuWithLabelAndButton
- selectors: AccountSelector
- filters: DateFilterPane
- config: Configuration system
- styles: Style loading system
"""

# Import from new hierarchical structure
from .base import BaseWidget
from .buttons import ActionButton, SecondaryButton, ExitButton
from .checkboxes import LabeledCheckBox
from .labels import HeadingLabel, SubheadingLabel
from .option_menus import OptionMenuWithLabel, OptionMenuWithLabelAndButton
from .selectors import AccountSelector
from .filters import DateFilterPane

# Import configuration and style systems
from .config import (
    BaseWidgetConfig, ButtonConfig, CheckBoxConfig,
    LabelConfig, OptionMenuConfig, SelectorConfig, FilterConfig,
    ConfigFactory
)
from .styles import StyleLoader

__all__ = [
    # Base
    'BaseWidget',

    # Buttons
    'ActionButton',
    'SecondaryButton',
    'ExitButton',

    # Checkboxes
    'LabeledCheckBox',

    # Labels
    'HeadingLabel',
    'SubheadingLabel',

    # Option Menus
    'OptionMenuWithLabel',
    'OptionMenuWithLabelAndButton',

    # Selectors
    'AccountSelector',

    # Filters
    'DateFilterPane',

    # Configuration
    'BaseWidgetConfig',
    'ButtonConfig',
    'CheckBoxConfig',
    'LabelConfig',
    'OptionMenuConfig',
    'SelectorConfig',
    'FilterConfig',
    'ConfigFactory',

    # Styles
    'StyleLoader'
]
