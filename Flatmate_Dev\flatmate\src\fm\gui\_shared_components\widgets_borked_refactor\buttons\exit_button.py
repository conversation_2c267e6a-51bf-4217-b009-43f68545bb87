"""
Exit Button Component

Enhanced exit button following App-Wide Widget Pattern with BaseWidget inheritance.
Maintains exact same API as original while adding configuration capabilities.
"""

from PySide6.QtWidgets import QPushButton, QVBoxLayout
from PySide6.QtCore import Signal

from ..base.base_widget import BaseWidget
from ..config.widget_config import ButtonConfig


class ExitButton(BaseWidget):
    """Enhanced exit button following App-Wide Widget Pattern."""
    
    # Button-specific signals (maintain compatibility)
    clicked = Signal()
    
    def __init__(self, text: str = "", parent=None):
        """Initialize the exit button.
        
        Args:
            text: Button text (maintains original API)
            parent: Parent widget
        """
        self._text = text
        super().__init__(parent)
    
    def _get_default_config(self) -> ButtonConfig:
        """Return default configuration for exit buttons."""
        return ButtonConfig(
            style_type="exit_btn",
            text=self._text
        )
    
    def _setup_ui(self):
        """Initialize button UI components."""
        # Create internal QPushButton to maintain exact behavior
        self._button = QPushButton(self)
        self._button.clicked.connect(self.clicked.emit)
        
        # Set up layout (zero margins to maintain exact size)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        layout.addWidget(self._button)
    
    def _apply_configuration(self):
        """Apply configuration to button."""
        self._button.setText(self._config.text)
        self._button.setEnabled(self._config.enabled)
        self._button.setProperty("type", self._config.style_type)
        
        if self._config.tooltip:
            self._button.setToolTip(self._config.tooltip)
        
        # Apply size constraints if configured
        if self._config.minimum_width:
            self._button.setMinimumWidth(self._config.minimum_width)
        if self._config.minimum_height:
            self._button.setMinimumHeight(self._config.minimum_height)
        if self._config.maximum_width:
            self._button.setMaximumWidth(self._config.maximum_width)
        if self._config.maximum_height:
            self._button.setMaximumHeight(self._config.maximum_height)
    
    def _apply_content(self):
        """Apply content to button."""
        if self._content:
            self._button.setText(str(self._content))
    
    # === BUTTON-SPECIFIC METHODS (Maintain original API) ===
    
    def text(self) -> str:
        """Get button text (QPushButton compatibility)."""
        return self._button.text()
    
    def setText(self, text: str):
        """Set button text (QPushButton compatibility)."""
        self._button.setText(text)
        self._config.text = text
    
    def click(self):
        """Programmatically click the button (QPushButton compatibility)."""
        self._button.click()
    
    def setEnabled(self, enabled: bool):
        """Set button enabled state (QPushButton compatibility)."""
        self._button.setEnabled(enabled)
        self._config.enabled = enabled
    
    def isEnabled(self) -> bool:
        """Get button enabled state (QPushButton compatibility)."""
        return self._button.isEnabled()
    
    def setToolTip(self, tooltip: str):
        """Set button tooltip (QPushButton compatibility)."""
        self._button.setToolTip(tooltip)
        self._config.tooltip = tooltip
    
    # === ENHANCED METHODS (New functionality) ===
    
    def set_text(self, text: str) -> 'ExitButton':
        """Set button text (chainable method)."""
        return self.configure(text=text)
    
    def set_style_type(self, style_type: str) -> 'ExitButton':
        """Set button style type (chainable method)."""
        return self.configure(style_type=style_type)
