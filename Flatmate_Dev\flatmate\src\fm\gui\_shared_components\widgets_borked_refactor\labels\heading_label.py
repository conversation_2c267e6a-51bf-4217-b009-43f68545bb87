"""
Heading Label Component

Enhanced heading label following App-Wide Widget Pattern with BaseWidget inheritance.
Provides consistent heading styling across the application.
"""

from PySide6.QtWidgets import QLabel, QVBoxLayout
from PySide6.QtCore import Qt

from ..base.base_widget import BaseWidget
from ..config.widget_config import LabelConfig


class HeadingLabel(BaseWidget):
    """Enhanced heading label following App-Wide Widget Pattern.
    
    A label specifically designed for main headings with consistent styling.
    Maintains compatibility with QLabel API while adding:
    - Configuration system
    - Content management
    - Style loading
    - Runtime flexibility
    """
    
    def __init__(self, text: str = "", parent=None):
        """Initialize the heading label.
        
        Args:
            text: Label text (maintains original API)
            parent: Parent widget
        """
        self._text = text
        super().__init__(parent)
    
    def _get_default_config(self) -> LabelConfig:
        """Return default configuration for heading labels."""
        return LabelConfig(
            style_type="heading",
            text=self._text,
            alignment="left"
        )
    
    def _setup_ui(self):
        """Initialize label UI components."""
        # Create internal QLabel to maintain exact behavior
        self._label = QLabel(self)
        
        # Set up layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self._label)
    
    def _apply_configuration(self):
        """Apply configuration to the label."""
        if not hasattr(self, '_label'):
            return
            
        config = self._config
        
        # Apply text
        self._label.setText(config.text)
        
        # Apply alignment
        alignment_map = {
            "left": Qt.AlignmentFlag.AlignLeft,
            "center": Qt.AlignmentFlag.AlignCenter,
            "right": Qt.AlignmentFlag.AlignRight
        }
        self._label.setAlignment(alignment_map.get(config.alignment, Qt.AlignmentFlag.AlignLeft))
        
        # Apply word wrap
        self._label.setWordWrap(config.word_wrap)
        
        # Apply object name for styling
        self._label.setObjectName(config.style_type)
        
        # Apply custom properties
        for prop, value in config.custom_properties.items():
            self._label.setProperty(prop, value)
        
        # Apply size constraints
        if config.minimum_width:
            self._label.setMinimumWidth(config.minimum_width)
        if config.minimum_height:
            self._label.setMinimumHeight(config.minimum_height)
        if config.maximum_width:
            self._label.setMaximumWidth(config.maximum_width)
        if config.maximum_height:
            self._label.setMaximumHeight(config.maximum_height)
        
        # Apply tooltip
        if config.tooltip:
            self._label.setToolTip(config.tooltip)
        
        # Apply enabled state
        self._label.setEnabled(config.enabled)
    
    def _apply_content(self):
        """Apply content to the label."""
        if hasattr(self, '_label') and self._content:
            self._label.setText(str(self._content))
    
    # Maintain QLabel API compatibility
    def setText(self, text: str):
        """Set the label text (QLabel compatibility)."""
        if hasattr(self, '_label'):
            self._label.setText(text)
        # Also update config for consistency
        if hasattr(self, '_config'):
            self._config.text = text
    
    def text(self) -> str:
        """Get the label text (QLabel compatibility)."""
        if hasattr(self, '_label'):
            return self._label.text()
        return self._config.text if hasattr(self, '_config') else ""
    
    def setAlignment(self, alignment):
        """Set the label alignment (QLabel compatibility)."""
        if hasattr(self, '_label'):
            self._label.setAlignment(alignment)
    
    def setWordWrap(self, wrap: bool):
        """Set word wrap (QLabel compatibility)."""
        if hasattr(self, '_label'):
            self._label.setWordWrap(wrap)
        # Also update config for consistency
        if hasattr(self, '_config'):
            self._config.word_wrap = wrap
    
    def setObjectName(self, name: str):
        """Set object name for styling (QLabel compatibility)."""
        if hasattr(self, '_label'):
            self._label.setObjectName(name)
        # Also update config for consistency
        if hasattr(self, '_config'):
            self._config.style_type = name
        super().setObjectName(name)
