"""
Option Menu With Label And Button Component

Enhanced option menu with label and button following App-Wide Widget Pattern with BaseWidget inheritance.
Maintains exact same API as original while adding configuration capabilities.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QLabel, QVBoxLayout, QComboBox, QPushButton

from ..base.base_widget import BaseWidget
from ..config.widget_config import OptionMenuConfig


class OptionMenuWithLabelAndButton(BaseWidget):
    """Enhanced option menu with label and button following App-Wide Widget Pattern.
    
    A combo box with a label and button in a vertical layout.
    Maintains exact same API as original while adding:
    - Configuration system
    - Content management
    - Style loading
    - Runtime flexibility
    
    Signals:
        option_changed: Emitted when the selected option changes
        button_clicked: Emitted when the button is clicked
    """
    
    # Option menu-specific signals (maintain compatibility)
    option_changed = Signal(str)
    button_clicked = Signal()
    
    def __init__(self, label_text="", options=None, button_text="Select...", parent=None):
        """Initialize the option menu with label and button.
        
        Args:
            label_text: Text to display above the combo box (maintains original API)
            options: List of options to display in the combo box (maintains original API)
            button_text: Text to display on the button (maintains original API)
            parent: Parent widget
        """
        self._label_text = label_text
        self._options = options or []
        self._button_text = button_text
        super().__init__(parent)
    
    def _get_default_config(self) -> OptionMenuConfig:
        """Return default configuration for option menus."""
        return OptionMenuConfig(
            options=self._options,
            placeholder_text="Select option..."
        )
    
    def _setup_ui(self):
        """Initialize option menu UI components."""
        # Main layout with reduced spacing (maintain exact same layout)
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(3)  # Reduced from 5 for height optimization
        
        # Label (maintain exact same structure)
        self.label = QLabel(self._label_text)
        self.label.setObjectName("subheading")
        layout.addWidget(self.label)
        
        # Combo box with fit-to-content sizing and height optimization (maintain exact same)
        self.combo_box = QComboBox()
        self.combo_box.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.combo_box.setMaximumHeight(26)  # Add max height to prevent crushing
        layout.addWidget(self.combo_box)
        
        # Button with height optimization (maintain exact same)
        self.button = QPushButton(self._button_text)
        self.button.setProperty("type", "select_btn")
        self.button.setMaximumHeight(26)  # Add max height for consistency
        layout.addWidget(self.button)
        
        # Connect signals (maintain exact same signal behavior)
        self.combo_box.currentTextChanged.connect(self.option_changed)
        self.button.clicked.connect(self.button_clicked)
    
    def _apply_configuration(self):
        """Apply configuration to option menu."""
        # Update options
        self.combo_box.clear()
        self.combo_box.addItems(self._config.options)
        
        # Set selected option if configured
        if self._config.selected_option:
            index = self.combo_box.findText(self._config.selected_option)
            if index >= 0:
                self.combo_box.setCurrentIndex(index)
        
        # Apply other configurations
        self.combo_box.setEnabled(self._config.enabled)
        self.button.setEnabled(self._config.enabled)
        
        if self._config.tooltip:
            self.combo_box.setToolTip(self._config.tooltip)
        
        # Apply size constraints if configured
        if self._config.minimum_width:
            self.setMinimumWidth(self._config.minimum_width)
        if self._config.minimum_height:
            self.setMinimumHeight(self._config.minimum_height)
        if self._config.maximum_width:
            self.setMaximumWidth(self._config.maximum_width)
        if self._config.maximum_height:
            self.setMaximumHeight(self._config.maximum_height)
    
    def _apply_content(self):
        """Apply content to option menu."""
        if self._content:
            if isinstance(self._content, list):
                self.combo_box.clear()
                self.combo_box.addItems([str(item) for item in self._content])
            else:
                # Set as selected option
                index = self.combo_box.findText(str(self._content))
                if index >= 0:
                    self.combo_box.setCurrentIndex(index)
    
    # === OPTION MENU-SPECIFIC METHODS (Maintain original API) ===
    
    def get_selected_option(self) -> str:
        """Get the currently selected option (original API)."""
        return self.combo_box.currentText()
    
    def set_selected_option(self, option: str):
        """Set the selected option (original API)."""
        index = self.combo_box.findText(option)
        if index >= 0:
            self.combo_box.setCurrentIndex(index)
            self._config.selected_option = option
    
    def set_button_enabled(self, enabled: bool):
        """Enable or disable the button (original API)."""
        self.button.setEnabled(enabled)
    
    def addItems(self, items: list):
        """Add items to combo box (Qt compatibility)."""
        self.combo_box.addItems(items)
        self._config.options.extend(items)
    
    def clear(self):
        """Clear all items from combo box (Qt compatibility)."""
        self.combo_box.clear()
        self._config.options.clear()
    
    def currentText(self) -> str:
        """Get current text (Qt compatibility)."""
        return self.combo_box.currentText()
    
    def setEnabled(self, enabled: bool):
        """Set enabled state (Qt compatibility)."""
        self.combo_box.setEnabled(enabled)
        self.button.setEnabled(enabled)
        self._config.enabled = enabled
    
    def isEnabled(self) -> bool:
        """Get enabled state (Qt compatibility)."""
        return self.combo_box.isEnabled()
    
    # === ENHANCED METHODS (New functionality) ===
    
    def set_options(self, options: list) -> 'OptionMenuWithLabelAndButton':
        """Set option list (chainable method)."""
        return self.configure(options=options)
    
    def set_label_text(self, text: str) -> 'OptionMenuWithLabelAndButton':
        """Set label text (chainable method)."""
        self.label.setText(text)
        return self
    
    def set_button_text(self, text: str) -> 'OptionMenuWithLabelAndButton':
        """Set button text (chainable method)."""
        self.button.setText(text)
        return self
