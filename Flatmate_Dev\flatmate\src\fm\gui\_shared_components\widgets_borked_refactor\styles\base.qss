/* Base styles for all shared widgets */

/* Base widget styling */
QWidget {
    font-family: "Segoe UI", Arial, sans-serif;
    font-size: 9pt;
}

/* Common button styling will be inherited by specific button types */
QPushButton {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px 8px;
    background-color: #f0f0f0;
}

QPushButton:hover {
    background-color: #e0e0e0;
}

QPushButton:pressed {
    background-color: #d0d0d0;
}

QPushButton:disabled {
    background-color: #f5f5f5;
    color: #999;
}

/* Common checkbox styling */
QCheckBox {
    spacing: 5px;
}

QCheckBox::indicator {
    width: 13px;
    height: 13px;
}

/* Common label styling */
QLabel {
    color: #333;
}

/* Common combo box styling */
QComboBox {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 2px 8px;
    background-color: white;
}

QComboBox:hover {
    border-color: #999;
}

QComboBox:focus {
    border-color: #0078d4;
}
