"""
Option Menu Components

Provides reusable combo box and dropdown components that follow the application's design patterns.
These components can be used across different modules for consistent option selection UI.
"""

from PySide6.QtCore import Signal
from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout, QComboBox, QPushButton


class OptionMenuWithLabel(QWidget):
    """
    A combo box with a label in a vertical layout.
    
    Signals:
        option_changed: Emitted when the selected option changes
    """
    
    option_changed = Signal(str)
    
    def __init__(self, label_text, options, parent=None):
        """
        Initialize the option menu with label.
        
        Args:
            label_text: Text to display above the combo box
            options: List of options to display in the combo box
            parent: Parent widget
        """
        super().__init__(parent)
        self._init_ui(label_text, options)
        self._connect_signals()
    
    def _init_ui(self, label_text, options):
        """Initialize the UI components with height optimization."""
        # Main layout with reduced spacing
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(3)  # Reduced from 5 for height optimization

        # Label
        self.label = QLabel(label_text)
        self.label.setObjectName("subheading")
        layout.addWidget(self.label)

        # Combo box with fit-to-content sizing and height optimization
        self.combo_box = QComboBox()
        self.combo_box.addItems(options)
        self.combo_box.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.combo_box.setMaximumHeight(26)  # Add max height to prevent crushing
        layout.addWidget(self.combo_box)
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.combo_box.currentTextChanged.connect(self.option_changed)
    
    def get_selected_option(self):
        """Get the currently selected option."""
        return self.combo_box.currentText()
    
    def set_selected_option(self, option):
        """Set the selected option."""
        index = self.combo_box.findText(option)
        if index >= 0:
            self.combo_box.setCurrentIndex(index)


class OptionMenuWithLabelAndButton(QWidget):
    """
    A combo box with a label and button in a vertical layout.
    
    Signals:
        option_changed: Emitted when the selected option changes
        button_clicked: Emitted when the button is clicked
    """
    
    option_changed = Signal(str)
    button_clicked = Signal()
    
    def __init__(self, label_text, options, button_text="Select...", parent=None):
        """
        Initialize the option menu with label and button.
        
        Args:
            label_text: Text to display above the combo box
            options: List of options to display in the combo box
            button_text: Text to display on the button
            parent: Parent widget
        """
        super().__init__(parent)
        self._init_ui(label_text, options, button_text)
        self._connect_signals()
    
    def _init_ui(self, label_text, options, button_text):
        """Initialize the UI components with height optimization."""
        # Main layout with reduced spacing
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(3)  # Reduced from 5 for height optimization

        # Label
        self.label = QLabel(label_text)
        self.label.setObjectName("subheading")
        layout.addWidget(self.label)

        # Combo box with fit-to-content sizing and height optimization
        self.combo_box = QComboBox()
        self.combo_box.addItems(options)
        self.combo_box.setSizeAdjustPolicy(QComboBox.SizeAdjustPolicy.AdjustToContents)
        self.combo_box.setMaximumHeight(26)  # Add max height to prevent crushing
        layout.addWidget(self.combo_box)

        # Button with height optimization
        self.button = QPushButton(button_text)
        self.button.setProperty("type", "select_btn")
        self.button.setMaximumHeight(26)  # Add max height for consistency
        layout.addWidget(self.button)
    
    def _connect_signals(self):
        """Connect widget signals."""
        self.combo_box.currentTextChanged.connect(self.option_changed)
        self.button.clicked.connect(self.button_clicked)
    
    def get_selected_option(self):
        """Get the currently selected option."""
        return self.combo_box.currentText()
    
    def set_selected_option(self, option):
        """Set the selected option."""
        index = self.combo_box.findText(option)
        if index >= 0:
            self.combo_box.setCurrentIndex(index)
    
    def set_button_enabled(self, enabled):
        """Enable or disable the button."""
        self.button.setEnabled(enabled)
