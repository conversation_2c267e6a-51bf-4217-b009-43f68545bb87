"""Left panel for the Categorize module."""

from datetime import datetime, timed<PERSON><PERSON>
from PySide6.QtCore import Signal
from PySide6.QtWidgets import (
    QVBoxLayout, QGroupBox, QFormLayout,
    QDateEdit, QComboBox, QCheckBox
)

from fm.gui._shared_components.base.base_pane import BasePane
from fm.modules.categorize.config import config
from fm.gui._shared_components.widgets.option_menus import OptionMenuWithLabelAndButton
from fm.gui._shared_components.widgets.buttons import ExitButton, ActionButton,SecondaryButton
from fm.gui._shared_components.widgets.filters.date_filter_pane import DateFilterPane
from fm.gui._shared_components.widgets.selectors import AccountSelector
from fm.gui._shared_components.utils.responsive_utils import ResponsiveSizing, ResponsiveWidget
from fm.core.data_services.db_io_service import DBIOService


class LeftPanelWidget(BasePane, ResponsiveWidget):
    """Left panel widget for the Categorize module with responsive design."""

    # Signals
    files_select_requested = Signal()
    load_db_requested = Signal()
    apply_filters_clicked = Signal(dict)
    cancel_clicked = Signal()

    def __init__(self, parent=None):
        """Initialize the left panel widget."""
        BasePane.__init__(self, parent)
        ResponsiveWidget.__init__(self)
        self._init_ui()
        self._connect_signals()
    
    def _init_ui(self):
        """Initialize the UI components with logical hierarchy."""
        # Ensure filter panel config defaults
        config.ensure_defaults({
            'categorize.filters.panel_min_width': 200,
            'categorize.filters.panel_max_width': 300,
            'categorize.filters.default_days_back': 365,  # Default to 1 year back
            'categorize.filters.remember_last_filter': True,
            'categorize.filters.show_calendar_popup': True
        })

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(12)

        min_width = config.get_value('categorize.filters.panel_min_width')
        max_width = config.get_value('categorize.filters.panel_max_width')
        self.setMinimumWidth(min_width)
        self.setMaximumWidth(max_width)

        # 1. LOAD FROM (Primary - choose data source first)
        self._create_load_from_section(layout)

        # 2. FILTER BY (Secondary - pre-filter before loading)
        self._create_filter_by_section(layout)

        # Add spacer to push action buttons to bottom
        layout.addStretch()

        # 3. LOAD DATA (Primary action button)
        self.load_data_btn = ActionButton("Load Data")
        self.load_data_btn.setEnabled(False)  # Disabled until source selected
        layout.addWidget(self.load_data_btn)

        # 4. EXIT (Bottom)
        self.cancel_btn = ExitButton("Exit")
        layout.addWidget(self.cancel_btn)

        # Initialize state
        self._update_filter_state()

        # Populate accounts since database is default
        self._populate_accounts()

    def _create_load_from_section(self, layout):
        """Create the 'Load From' section at the top."""
        from PySide6.QtWidgets import QLabel, QFrame

        # Section header
        load_header = QLabel("1. Load From")
        load_header.setObjectName("section_header")
        load_header.setStyleSheet("""
            QLabel#section_header {
                color: #CCCCCC;
                font-size: 13px;
                font-weight: bold;
                margin-bottom: 4px;
                margin-top: 8px;
            }
        """)
        layout.addWidget(load_header)

        # Data source selection
        self.load_source_menu = OptionMenuWithLabelAndButton(
            label_text="Source:",
            options=["database", "file"],
            button_text="Select"
        )
        # Set default to database
        self.load_source_menu.combo_box.setCurrentText("database")
        layout.addWidget(self.load_source_menu)

    def _create_filter_by_section(self, layout):
        """Create the 'Filter By' section in the middle."""
        from PySide6.QtWidgets import QLabel, QFrame

        # Container frame for filters with green piping
        self.filter_container = QFrame()
        self.filter_container.setObjectName("filter_container")
        self.filter_container.setStyleSheet("""
            QFrame#filter_container {
                background-color: #242424;
                border: 2px solid #3B8A45;
                border-radius: 8px;
                padding: 12px;
                margin: 8px 0px;
            }
        """)

        filter_layout = QVBoxLayout(self.filter_container)
        filter_layout.setContentsMargins(8, 8, 8, 8)
        filter_layout.setSpacing(12)

        # Filter header inside the container
        filter_header = QLabel("2. Filter By...")
        filter_header.setObjectName("filter_header")
        filter_header.setStyleSheet("""
            QLabel#filter_header {
                color: #3B8A45;
                font-size: 13px;
                font-weight: bold;
                margin-bottom: 4px;
            }
        """)
        filter_layout.addWidget(filter_header)

        # Date filter
        self.date_filter_pane = DateFilterPane()
        filter_layout.addWidget(self.date_filter_pane)

        # Account filter
        self.account_selector = AccountSelector()
        filter_layout.addWidget(self.account_selector)

        # Clear filters button
        self.clear_filters_btn = SecondaryButton("Clear All Filters")
        filter_layout.addWidget(self.clear_filters_btn)

        layout.addWidget(self.filter_container)

    def _update_filter_state(self):
        """Update the enabled state of filter components based on data source."""
        source = self.load_source_menu.combo_box.currentText()

        # Enable/disable filters based on source
        filters_enabled = source == "database"
        self.filter_container.setEnabled(filters_enabled)

        # Enable load data button if source is selected
        self.load_data_btn.setEnabled(bool(source))

        # Update visual feedback
        if not filters_enabled:
            self.filter_container.setStyleSheet("""
                QFrame#filter_container {
                    background-color: #1A1A1A;
                    border: 2px solid #2A2A2A;
                    border-radius: 8px;
                    padding: 12px;
                    margin: 8px 0px;
                }
            """)
        else:
            self.filter_container.setStyleSheet("""
                QFrame#filter_container {
                    background-color: #242424;
                    border: 2px solid #3B8A45;
                    border-radius: 8px;
                    padding: 12px;
                    margin: 8px 0px;
                }
            """)

    def _connect_signals(self):
        """Connect internal signals."""
        # Data source selection
        self.load_source_menu.combo_box.currentTextChanged.connect(self._on_source_changed)
        self.load_source_menu.button_clicked.connect(self._on_select_file_clicked)

        # Load data button
        self.load_data_btn.clicked.connect(self._on_load_data_clicked)

        # Filter controls
        self.clear_filters_btn.clicked.connect(self._on_clear_filters)
        self.cancel_btn.clicked.connect(self.cancel_clicked)

        # Connect date filter signals
        self.date_filter_pane.filter_changed.connect(self._on_date_filter_changed)
        self.date_filter_pane.preset_selected.connect(self._on_date_preset_selected)

        # Connect account selector signals
        self.account_selector.selection_changed.connect(self._on_account_selection_changed)
    
    def _on_source_changed(self, source_text):
        """Handle data source selection change."""
        self._update_filter_state()

        # If database is selected, populate accounts
        if source_text == "database":
            self._populate_accounts()

    def _on_select_file_clicked(self):
        """Handle the select file button click (only for file source)."""
        source = self.load_source_menu.combo_box.currentText()
        if source == "file":
            self.files_select_requested.emit()

    def _on_load_data_clicked(self):
        """Handle the main Load Data button click."""
        source = self.load_source_menu.combo_box.currentText()

        if source == "database":
            # Get current filters and load from database
            filters = self.get_filters()
            self.load_db_requested.emit()
        elif source == "file":
            # For file source, we need file selection first
            self.files_select_requested.emit()

    def _on_date_filter_changed(self, filter_data):
        """Handle date filter changes."""
        # Store the current date filter for when Apply is clicked
        self._current_date_filter = filter_data

        # Auto-apply if configured (future enhancement)
        # For now, just store the filter data

    def _on_date_preset_selected(self, preset_id):
        """Handle date preset selection."""
        # Could show feedback or save preference here
        pass

    def _on_apply_filters(self):
        """Apply filters and emit signal."""
        filters = {}

        # Get date filter from the DateFilterPane
        date_filter = self.date_filter_pane.get_current_filter()
        if date_filter['start_date'] and date_filter['end_date']:
            filters['start_date'] = date_filter['start_date']
            filters['end_date'] = date_filter['end_date']

        # Add account filter if not "All Accounts"
        account_data = self.account_combo.currentData()
        if account_data is not None:
            filters['account'] = account_data

        self.apply_filters_clicked.emit(filters)

    def _populate_accounts(self):
        """Populate the account selector with available accounts from database."""
        try:
            # Get unique account numbers from database
            db_service = DBIOService()
            accounts = db_service.get_unique_account_numbers()

            if accounts:
                self.account_selector.set_available_accounts(accounts)
            else:
                # If no accounts in database, show placeholder message
                self.account_selector.set_available_accounts(["No accounts found in database"])

        except Exception as e:
            # Fallback to placeholder accounts if database query fails
            placeholder_accounts = [
                "Checking-****************",
                "Savings-****************",
                "Credit-****************",
                "Investment-****************",
                "Business-****************"
            ]
            self.account_selector.set_available_accounts(placeholder_accounts)
            print(f"Warning: Could not load accounts from database: {e}")
            print("Using placeholder accounts instead")

    def _on_account_selection_changed(self, selected_accounts):
        """Handle account selection changes."""
        # Could trigger auto-refresh or show feedback here
        pass

    def _on_clear_filters(self):
        """Clear all filters and reset to defaults."""
        # Reset date filter to "All"
        self.date_filter_pane.set_preset('all')

        # Reset account selection to "All Accounts"
        self.account_selector.set_selected_accounts(set())
    

    
    def get_filters(self):
        """Get the current filter settings."""
        filters = {}

        # Get date filter from the DateFilterPane
        date_filter = self.date_filter_pane.get_current_filter()
        if date_filter['start_date'] and date_filter['end_date']:
            filters['start_date'] = date_filter['start_date']
            filters['end_date'] = date_filter['end_date']

        # Add account filter if specific accounts selected
        selected_accounts = self.account_selector.get_selected_accounts()
        if selected_accounts:  # If not empty set (empty = all accounts)
            filters['accounts'] = selected_accounts

        return filters
    
    def show_component(self):
        """Show this component."""
        self.show()
        self.activate()
    
    def hide_component(self):
        """Hide this component."""
        self.hide()
        self.deactivate()
